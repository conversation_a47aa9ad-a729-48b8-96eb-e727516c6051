# Run Biome format on staged files to auto-fix what we can
echo "🔧 Auto-formatting staged files with Biome..."
bunx biome format --write --staged

# Run oxlint for fast linting check (non-blocking)
echo "🔍 Running oxlint checks..."
bun run lint 2>/dev/null || echo "⚠️  oxlint found some issues (non-blocking)"

# Optional: Run full build check only if STRICT_COMMIT is set
if [ "$STRICT_COMMIT" = "true" ]; then
    echo "🏗️  Running build check (strict mode)..."
    bun run build
fi

# Run tests if TEST_ON_COMMIT is set
if [ "$TEST_ON_COMMIT" = "true" ]; then
    echo "🧪 Running tests..."
    bun run test:run
fi

echo "✅ Pre-commit checks completed!"
